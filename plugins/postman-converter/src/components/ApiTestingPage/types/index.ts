// Core types re-exported from main types
export {
  HttpMethod,
  ApiRequest,
  ApiResponse,
  ApiEnvironment,
  ApiCollection,
  ApiFolder,
  Collection,
  TestResult as ApiTestResult
} from '../../../types';

// Component-specific interfaces

// Props for the main ApiTestingPage component
export interface ApiTestingPageProps {
  // Currently no props, but defined for future extensibility
}

// Props for CollectionsSidebar component
export interface CollectionsSidebarProps {
  collections: ApiCollection[];
  selectedItemId: string | null;
  expandedFolders: Record<string, boolean>;
  unsavedCollections: Set<string>;
  isSaving: Record<string, boolean>;
  collectionsLoading: boolean;
  collectionsError: Error | null;
  onItemSelect: (itemId: string) => void;
  onFolderToggle: (folderId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
  onSaveCollection: (collectionId: string) => Promise<void>;
  onAddCollection: () => void;
}

// Props for RequestPanel component
export interface RequestPanelProps {
  currentRequest: ApiRequest;
  selectedEnvironment?: ApiEnvironment;
  tabValue: number;
  isLoading: boolean;
  onRequestChange: (request: ApiRequest) => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSendRequest: () => Promise<void>;
  onSaveTests: (testScript: string) => void;
  onSavePreRequestScript: (script: string) => void;
}

// Props for ResponsePanel component
export interface ResponsePanelProps {
  currentResponse: ApiResponse | null;
  responseTabValue: number;
  testResults: TestResult[];
  isRunningTests: boolean;
  testError: string | null;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onRunTests: (testScript: string) => Promise<void>;
}

// Props for EnvironmentManager component
export interface EnvironmentManagerProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  environmentDialogOpen: boolean;
  onEnvironmentChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onEnvironmentsUpdate: (environments: ApiEnvironment[]) => void;
  onSelectedEnvironmentChange: (environmentId: string) => void;
  onDialogToggle: (open: boolean) => void;
}

// Props for ContextMenu component
export interface ContextMenuProps {
  contextMenu: ContextMenuState | null;
  collections: ApiCollection[];
  onClose: () => void;
  onAddFolder: (collectionId: string, parentFolderId?: string) => void;
  onAddRequest: (collectionId: string, folderId?: string) => void;
  onRenameCollection: (collectionId: string, currentName: string) => void;
  onRenameFolder: (collectionId: string, folderId: string, currentName: string) => void;
  onRenameRequest: (collectionId: string, requestId: string, currentName: string) => void;
  onEditCollection: (collectionId: string) => void;
  onDuplicateRequest: (requestId: string) => void;
  onDeleteItem: (itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
}

// State interfaces
export interface ContextMenuState {
  mouseX: number;
  mouseY: number;
  itemId: string;
  itemType: 'collection' | 'folder' | 'request';
}

export interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

export interface RenameDialogData {
  itemType: 'collection' | 'folder' | 'request';
  itemId: string;
  collectionId: string;
  currentName: string;
}

// Test-related types
export interface TestResult {
  id: string;
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

// Hook return types
export interface UseCollectionsReturn {
  collections: ApiCollection[];
  selectedItemId: string | null;
  expandedFolders: Record<string, boolean>;
  unsavedCollections: Set<string>;
  isSaving: Record<string, boolean>;
  collectionsLoading: boolean;
  collectionsError: Error | null;
  setSelectedItemId: (itemId: string | null) => void;
  setExpandedFolders: (folders: Record<string, boolean>) => void;
  markCollectionAsUnsaved: (collectionId: string) => void;
  markCollectionAsSaved: (collectionId: string) => void;
  handleItemSelect: (itemId: string) => void;
  handleFolderToggle: (folderId: string) => void;
  handleSaveCollection: (collectionId: string) => Promise<void>;
  handleAddCollection: () => void;
  handleCreateFolder: (folderName: string, parentId: string | null, collectionId: string) => void;
  handleCreateRequest: (requestName: string, method: HttpMethod, url: string, parentId: string | null, collectionId: string) => void;
  handleRename: (newName: string, renameData: RenameDialogData) => void;
  handleDeleteItem: (itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
  handleDuplicateRequest: (requestId: string) => void;
}

export interface UseEnvironmentsReturn {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  selectedEnvironment?: ApiEnvironment;
  environmentDialogOpen: boolean;
  setEnvironments: (environments: ApiEnvironment[]) => void;
  setCurrentEnvironment: (environmentId: string) => void;
  setSelectedEnvironmentId: (environmentId: string) => void;
  setEnvironmentDialogOpen: (open: boolean) => void;
  handleEnvironmentChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
}

export interface UseRequestReturn {
  currentRequest: ApiRequest;
  currentResponse: ApiResponse | null;
  isLoading: boolean;
  tabValue: number;
  responseTabValue: number;
  setCurrentRequest: (request: ApiRequest) => void;
  setCurrentResponse: (response: ApiResponse | null) => void;
  setTabValue: (value: number) => void;
  setResponseTabValue: (value: number) => void;
  handleMethodChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  handleUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSendRequest: () => Promise<void>;
  handleTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
}

export interface UseTestExecutionReturn {
  testResults: TestResult[];
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testError: string | null;
  preRequestScriptError: string | null;
  isSavingPreRequestScript: boolean;
  handleGenerateTests: () => void;
  handleRunTests: (testScript: string) => Promise<void>;
  handleSaveTests: (testScript: string) => void;
  handleSavePreRequestScript: (script: string) => void;
}

// Utility function types
export type VariableResolver = (text: string, environment?: ApiEnvironment) => string;
export type RequestSender = (request: ApiRequest, environment?: ApiEnvironment) => Promise<ApiResponse>;
export type CollectionConverter = (collection: Collection) => Promise<ApiCollection>;
export type PostmanConverter = (apiCollection: ApiCollection) => any;

// Event handler types
export type ItemSelectHandler = (itemId: string) => void;
export type FolderToggleHandler = (folderId: string) => void;
export type ContextMenuHandler = (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
export type TabChangeHandler = (event: React.ChangeEvent<{}>, newValue: number) => void;
export type RequestChangeHandler = (request: ApiRequest) => void;
export type EnvironmentChangeHandler = (event: React.ChangeEvent<{ value: unknown }>) => void;
