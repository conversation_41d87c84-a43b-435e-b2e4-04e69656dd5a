import React from 'react';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

// Icons
import SettingsIcon from '@material-ui/icons/Settings';

import { EnvironmentManagerProps } from '../../types';

const useStyles = makeStyles(theme => ({
  environmentSelector: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  envSelect: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  envActions: {
    display: 'flex',
  },
}));

export const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({
  environments,
  currentEnvironment,
  selectedEnvironmentId,
  environmentDialogOpen,
  onEnvironmentChange,
  onEnvironmentsUpdate,
  onSelectedEnvironmentChange,
  onDialogToggle,
}) => {
  const classes = useStyles();

  return (
    <Box className={classes.environmentSelector}>
      <Typography variant="subtitle2" style={{ marginRight: '16px', minWidth: '100px' }}>
        Environment:
      </Typography>
      <FormControl className={classes.envSelect}>
        <InputLabel id="environment-select-label">Select Environment</InputLabel>
        <Select
          labelId="environment-select-label"
          id="environment-select"
          value={currentEnvironment}
          onChange={onEnvironmentChange}
        >
          <MenuItem value="">
            <em>No Environment</em>
          </MenuItem>
          {environments.map(env => (
            <MenuItem key={env.id} value={env.id}>
              {env.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <div className={classes.envActions}>
        <Button
          variant="outlined"
          size="small"
          startIcon={<SettingsIcon />}
          onClick={() => onDialogToggle(true)}
        >
          Manage
        </Button>
      </div>
    </Box>
  );
};
