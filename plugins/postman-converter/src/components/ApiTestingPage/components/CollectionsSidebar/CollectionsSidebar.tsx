import React from 'react';
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Tooltip,
  Typography,
  Badge,
  Collapse,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import {
  EmptyState,
  ErrorPanel,
} from '@backstage/core-components';

// Icons
import AddIcon from '@material-ui/icons/Add';
import SaveIcon from '@material-ui/icons/Save';
import FolderIcon from '@material-ui/icons/Folder';
import FolderOpenIcon from '@material-ui/icons/FolderOpen';
import DescriptionIcon from '@material-ui/icons/Description';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import MoreVertIcon from '@material-ui/icons/MoreVert';

import { CollectionsSidebarProps } from '../../types';
import { RenderFolder } from '../RenderFolder';
import { getMethodColor } from '../../utils/collectionUtils';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
  collectionTree: {
    maxHeight: 'calc(100vh - 300px)',
    overflow: 'auto',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
  },
}));

export const CollectionsSidebar: React.FC<CollectionsSidebarProps> = ({
  collections,
  selectedItemId,
  expandedFolders,
  unsavedCollections,
  isSaving,
  collectionsLoading,
  collectionsError,
  onItemSelect,
  onFolderToggle,
  onContextMenu,
  onSaveCollection,
  onAddCollection,
}) => {
  const classes = useStyles();

  const renderContent = () => {
    if (collectionsLoading) {
      return (
        <div className={classes.loadingContainer}>
          <CircularProgress />
        </div>
      );
    }

    if (collectionsError) {
      return <ErrorPanel error={collectionsError} />;
    }

    if (collections.length === 0) {
      return (
        <EmptyState
          missing="data"
          title="No collections"
          description="You haven't created any collections yet."
          action={
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={onAddCollection}
            >
              Create Collection
            </Button>
          }
        />
      );
    }

    return (
      <div className={classes.collectionTree}>
        <List component="nav" aria-label="collections">
          {collections.map(collection => {
            // Check if collection has any content
            const hasContent = collection.folders.length > 0 || Object.keys(collection.requests).length > 0;
            const isExpanded = expandedFolders[`collection_${collection.id}`] || false;
            const hasUnsavedChanges = unsavedCollections.has(collection.id);
            const isSavingCollection = isSaving[collection.id] || false;

            return (
              <React.Fragment key={collection.id}>
                <ListItem
                  button
                  selected={selectedItemId === collection.id}
                  onClick={() => onItemSelect(collection.id)}
                  onContextMenu={(e) => onContextMenu(e, collection.id, 'collection')}
                >
                  <ListItemIcon onClick={(e) => {
                    e.stopPropagation();
                    onFolderToggle(`collection_${collection.id}`);
                  }}>
                    {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                  </ListItemIcon>
                  <ListItemIcon>
                    {hasUnsavedChanges ? (
                      <Badge color="secondary" variant="dot">
                        <FolderIcon color="primary" />
                      </Badge>
                    ) : (
                      <FolderIcon color="primary" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center">
                        <span>{collection.name}</span>
                        {hasUnsavedChanges && (
                          <Chip
                            label="Unsaved"
                            size="small"
                            color="secondary"
                            style={{ marginLeft: '8px', fontSize: '0.7rem' }}
                          />
                        )}
                      </Box>
                    }
                  />
                  {hasUnsavedChanges && (
                    <Tooltip title="Save collection changes">
                      <IconButton
                        size="small"
                        color="primary"
                        disabled={isSavingCollection}
                        onClick={(e) => {
                          e.stopPropagation();
                          onSaveCollection(collection.id);
                        }}
                        style={{ marginRight: '4px' }}
                      >
                        {isSavingCollection ? (
                          <CircularProgress size={16} />
                        ) : (
                          <SaveIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                  )}
                  <IconButton
                    edge="end"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onContextMenu(e, collection.id, 'collection');
                    }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </ListItem>

                {/* Render folders and requests */}
                <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {/* Render orphaned requests at the root level */}
                    {collection._orphanedRequests && collection._orphanedRequests.length > 0 && (
                      <div>
                        <ListItem style={{ paddingLeft: '16px', backgroundColor: '#f5f5f5' }}>
                          <ListItemText
                            primary="Root Requests"
                            secondary={`${collection._orphanedRequests.length} requests not in folders`}
                          />
                        </ListItem>
                        {collection._orphanedRequests.map(requestId => {
                          const request = collection.requests[requestId];
                          if (!request) return null;

                          return (
                            <ListItem
                              key={requestId}
                              button
                              style={{ paddingLeft: '32px' }}
                              selected={selectedItemId === requestId}
                              onClick={() => onItemSelect(requestId)}
                              onContextMenu={(e) => onContextMenu(e, requestId, 'request')}
                            >
                              <ListItemIcon>
                                <DescriptionIcon />
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center">
                                    <Chip
                                      label={request.method}
                                      size="small"
                                      style={{
                                        backgroundColor: getMethodColor(request.method),
                                        color: 'white',
                                        marginRight: '8px',
                                        fontWeight: 'bold',
                                        minWidth: '60px',
                                        textAlign: 'center'
                                      }}
                                    />
                                    <span>{request.name}</span>
                                  </Box>
                                }
                                secondary={request.url}
                                primaryTypographyProps={{
                                  style: {
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }
                                }}
                                secondaryTypographyProps={{
                                  style: {
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }
                                }}
                              />
                              <IconButton
                                edge="end"
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onContextMenu(e, requestId, 'request');
                                }}
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </ListItem>
                          );
                        })}
                      </div>
                    )}

                    {/* Render folders */}
                    {collection.folders.map(folder => (
                      <RenderFolder
                        key={folder.id}
                        folder={folder}
                        collection={collection}
                        level={1}
                        expandedFolders={expandedFolders}
                        selectedItemId={selectedItemId}
                        onFolderToggle={onFolderToggle}
                        onItemSelect={onItemSelect}
                        onContextMenu={onContextMenu}
                      />
                    ))}

                    {/* Show message if collection is empty */}
                    {!hasContent && !collection._orphanedRequests?.length && (
                      <ListItem style={{ paddingLeft: '16px' }}>
                        <ListItemText
                          primary="Empty collection"
                          secondary="This collection has no requests or folders"
                          primaryTypographyProps={{ style: { color: '#999' } }}
                          secondaryTypographyProps={{ style: { color: '#bbb' } }}
                        />
                      </ListItem>
                    )}
                  </List>
                </Collapse>
              </React.Fragment>
            );
          })}
        </List>
      </div>
    );
  };

  return (
    <Paper className={classes.paper}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="h6">
          Collections
        </Typography>
        {unsavedCollections.size > 0 && (
          <Button
            size="small"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={async () => {
              // Save all collections with unsaved changes
              const savePromises = Array.from(unsavedCollections).map(collectionId =>
                onSaveCollection(collectionId)
              );
              await Promise.all(savePromises);
            }}
            disabled={Object.values(isSaving).some(saving => saving)}
          >
            Save All ({unsavedCollections.size})
          </Button>
        )}
      </Box>
      <Divider className={classes.divider} />
      {renderContent()}
    </Paper>
  );
};
