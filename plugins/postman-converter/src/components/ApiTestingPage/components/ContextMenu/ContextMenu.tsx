import React from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@material-ui/core';

// Icons
import AddIcon from '@material-ui/icons/Add';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import FileCopyIcon from '@material-ui/icons/FileCopy';
import FolderIcon from '@material-ui/icons/Folder';
import DescriptionIcon from '@material-ui/icons/Description';

import { ContextMenuProps } from '../../types';

export const ContextMenu: React.FC<ContextMenuProps> = ({
  contextMenu,
  collections,
  onClose,
  onAddFolder,
  onAddRequest,
  onRenameCollection,
  onRenameFolder,
  onRenameRequest,
  onEditCollection,
  onDuplicateRequest,
  onDeleteItem,
}) => {
  if (!contextMenu) {
    return null;
  }

  const { mouseX, mouseY, itemId, itemType } = contextMenu;

  const handleClose = () => {
    onClose();
  };

  const handleAddFolder = () => {
    if (itemType === 'collection') {
      onAddFolder(itemId);
    } else if (itemType === 'folder') {
      onAddFolder(itemId.split('_')[0], itemId); // Extract collection ID and use folder as parent
    }
    handleClose();
  };

  const handleAddRequest = () => {
    if (itemType === 'collection') {
      onAddRequest(itemId);
    } else if (itemType === 'folder') {
      onAddRequest(itemId.split('_')[0], itemId); // Extract collection ID and use folder as parent
    }
    handleClose();
  };

  const handleRename = () => {
    if (itemType === 'collection') {
      const collection = collections.find(c => c.id === itemId);
      if (collection) {
        onRenameCollection(itemId, collection.name);
      }
    } else if (itemType === 'folder') {
      // Find the folder and its collection
      let foundFolder = null;
      let foundCollection = null;
      
      for (const collection of collections) {
        const findFolder = (folders: any[]): any => {
          for (const folder of folders) {
            if (folder.id === itemId) {
              return folder;
            }
            if (folder.folders && folder.folders.length > 0) {
              const nested = findFolder(folder.folders);
              if (nested) return nested;
            }
          }
          return null;
        };
        
        foundFolder = findFolder(collection.folders);
        if (foundFolder) {
          foundCollection = collection;
          break;
        }
      }
      
      if (foundFolder && foundCollection) {
        onRenameFolder(foundCollection.id, itemId, foundFolder.name);
      }
    } else if (itemType === 'request') {
      // Find the request and its collection
      let foundRequest = null;
      let foundCollection = null;
      
      for (const collection of collections) {
        if (collection.requests[itemId]) {
          foundRequest = collection.requests[itemId];
          foundCollection = collection;
          break;
        }
      }
      
      if (foundRequest && foundCollection) {
        onRenameRequest(foundCollection.id, itemId, foundRequest.name);
      }
    }
    handleClose();
  };

  const handleEdit = () => {
    if (itemType === 'collection') {
      onEditCollection(itemId);
    }
    handleClose();
  };

  const handleDuplicate = () => {
    if (itemType === 'request') {
      onDuplicateRequest(itemId);
    }
    handleClose();
  };

  const handleDelete = () => {
    onDeleteItem(itemId, itemType);
    handleClose();
  };

  return (
    <Menu
      keepMounted
      open={Boolean(contextMenu)}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu
          ? { top: mouseY, left: mouseX }
          : undefined
      }
    >
      {/* Collection context menu */}
      {itemType === 'collection' && [
        <MenuItem key="add-folder" onClick={handleAddFolder}>
          <ListItemIcon>
            <FolderIcon />
          </ListItemIcon>
          <ListItemText primary="Add Folder" />
        </MenuItem>,
        <MenuItem key="add-request" onClick={handleAddRequest}>
          <ListItemIcon>
            <DescriptionIcon />
          </ListItemIcon>
          <ListItemText primary="Add Request" />
        </MenuItem>,
        <Divider key="divider1" />,
        <MenuItem key="edit" onClick={handleEdit}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText primary="Edit Collection" />
        </MenuItem>,
        <MenuItem key="rename" onClick={handleRename}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText primary="Rename" />
        </MenuItem>,
        <Divider key="divider2" />,
        <MenuItem key="delete" onClick={handleDelete}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText primary="Delete Collection" />
        </MenuItem>,
      ]}

      {/* Folder context menu */}
      {itemType === 'folder' && [
        <MenuItem key="add-folder" onClick={handleAddFolder}>
          <ListItemIcon>
            <FolderIcon />
          </ListItemIcon>
          <ListItemText primary="Add Folder" />
        </MenuItem>,
        <MenuItem key="add-request" onClick={handleAddRequest}>
          <ListItemIcon>
            <DescriptionIcon />
          </ListItemIcon>
          <ListItemText primary="Add Request" />
        </MenuItem>,
        <Divider key="divider1" />,
        <MenuItem key="rename" onClick={handleRename}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText primary="Rename" />
        </MenuItem>,
        <Divider key="divider2" />,
        <MenuItem key="delete" onClick={handleDelete}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText primary="Delete Folder" />
        </MenuItem>,
      ]}

      {/* Request context menu */}
      {itemType === 'request' && [
        <MenuItem key="duplicate" onClick={handleDuplicate}>
          <ListItemIcon>
            <FileCopyIcon />
          </ListItemIcon>
          <ListItemText primary="Duplicate" />
        </MenuItem>,
        <MenuItem key="rename" onClick={handleRename}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText primary="Rename" />
        </MenuItem>,
        <Divider key="divider" />,
        <MenuItem key="delete" onClick={handleDelete}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText primary="Delete Request" />
        </MenuItem>,
      ]}
    </Menu>
  );
};
