import React from 'react';
import {
  Box,
  Chip,
  Grid,
  Paper,
  Tab,
  Tabs,
  Typography,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { CodeSnippet } from '@backstage/core-components';
import { Alert } from '@material-ui/lab';

import { ResponsePanelProps } from '../../types';
import { TabPanel } from '../TabPanel';
import { TestResultsPanel } from '../../TestResultsPanel';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    marginTop: theme.spacing(2),
  },
  statusSuccess: {
    color: theme.palette.success.main,
  },
  statusError: {
    color: theme.palette.error.main,
  },
  statusInfo: {
    color: theme.palette.info.main,
  },
  statusWarning: {
    color: theme.palette.warning.main,
  },
  responseTime: {
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  tabPanel: {
    padding: theme.spacing(2),
  },
}));

export const ResponsePanel: React.FC<ResponsePanelProps> = ({
  currentResponse,
  responseTabValue,
  testResults,
  isRunningTests,
  testError,
  onResponseTabChange,
  onRunTests,
}) => {
  const classes = useStyles();

  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return classes.statusSuccess;
    if (status >= 400 && status < 500) return classes.statusError;
    if (status >= 500) return classes.statusError;
    if (status >= 300 && status < 400) return classes.statusWarning;
    return classes.statusInfo;
  };

  if (!currentResponse) {
    return (
      <Paper className={classes.paper}>
        <Alert severity="info">
          Send a request to see the response here.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper className={classes.paper}>
      {/* Response Status */}
      <Box display="flex" alignItems="center" mb={2}>
        <Typography variant="h6">Response</Typography>
        <Chip
          label={`${currentResponse.status} ${currentResponse.statusText}`}
          className={getStatusColor(currentResponse.status)}
          style={{ marginLeft: '16px' }}
        />
        {currentResponse.time !== undefined && (
          <Typography className={classes.responseTime}>
            {currentResponse.time}ms
          </Typography>
        )}
        {currentResponse.size !== undefined && (
          <Typography className={classes.responseTime}>
            {(currentResponse.size / 1024).toFixed(2)} KB
          </Typography>
        )}
      </Box>

      {/* Response Tabs */}
      <Box>
        <Tabs
          value={responseTabValue}
          onChange={onResponseTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Body" />
          <Tab label="Headers" />
          <Tab label="Test Results" />
        </Tabs>

        {/* Body tab */}
        <TabPanel value={responseTabValue} index={0}>
          <CodeSnippet
            text={currentResponse.body || 'No response body'}
            language="json"
            showCopyCodeButton
          />
        </TabPanel>

        {/* Headers tab */}
        <TabPanel value={responseTabValue} index={1}>
          <Box>
            {Object.entries(currentResponse.headers || {}).map(([key, value]) => (
              <Box key={key} display="flex" mb={1}>
                <Typography variant="body2" style={{ fontWeight: 'bold', minWidth: '200px' }}>
                  {key}:
                </Typography>
                <Typography variant="body2" style={{ marginLeft: '8px' }}>
                  {value}
                </Typography>
              </Box>
            ))}
            {Object.keys(currentResponse.headers || {}).length === 0 && (
              <Typography variant="body2" color="textSecondary">
                No response headers
              </Typography>
            )}
          </Box>
        </TabPanel>

        {/* Test Results tab */}
        <TabPanel value={responseTabValue} index={2}>
          <TestResultsPanel
            results={testResults}
            isRunning={isRunningTests}
            error={testError}
          />
        </TabPanel>
      </Box>
    </Paper>
  );
};
