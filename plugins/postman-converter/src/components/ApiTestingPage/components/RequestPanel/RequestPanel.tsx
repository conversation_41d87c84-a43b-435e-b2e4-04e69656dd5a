import React from 'react';
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  CircularProgress,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

// Icons
import SendIcon from '@material-ui/icons/Send';

import { RequestPanelProps } from '../../types';
import { TabPanel } from '../TabPanel';
import { ParamsTab } from '../ParamsTab';
import { HeadersTab } from '../HeadersTab';
import { BodyTab } from '../BodyTab';
import { AuthTab } from '../AuthTab';
import { TestGeneratorPanel } from '../../TestGeneratorPanel';
import { PreRequestScriptPanel } from '../../PreRequestScriptPanel';
import { getUrlHelperText } from '../../utils/requestUtils';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  urlBar: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  methodSelect: {
    width: 120,
    marginRight: theme.spacing(2),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(2),
  },
  sendButton: {
    marginLeft: theme.spacing(1),
  },
  tabPanel: {
    padding: theme.spacing(2),
  },
}));

export const RequestPanel: React.FC<RequestPanelProps> = ({
  currentRequest,
  selectedEnvironment,
  tabValue,
  isLoading,
  onRequestChange,
  onTabChange,
  onSendRequest,
  onSaveTests,
  onSavePreRequestScript,
}) => {
  const classes = useStyles();

  const handleMethodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onRequestChange({
      ...currentRequest,
      method: event.target.value as any,
    });
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRequestChange({
      ...currentRequest,
      url: event.target.value,
    });
  };

  const handleParamsChange = (params: any[]) => {
    onRequestChange({
      ...currentRequest,
      params,
    });
  };

  const handleHeadersChange = (headers: any[]) => {
    onRequestChange({
      ...currentRequest,
      headers,
    });
  };

  const handleBodyChange = (body: any) => {
    onRequestChange({
      ...currentRequest,
      body,
    });
  };

  const handleAuthChange = (auth: any) => {
    onRequestChange({
      ...currentRequest,
      auth,
    });
  };

  return (
    <Paper className={classes.paper}>
      {/* URL Bar */}
      <div className={classes.urlBar}>
        <FormControl className={classes.methodSelect}>
          <InputLabel id="method-select-label">Method</InputLabel>
          <Select
            labelId="method-select-label"
            id="method-select"
            value={currentRequest.method}
            onChange={handleMethodChange}
          >
            <MenuItem value="GET">GET</MenuItem>
            <MenuItem value="POST">POST</MenuItem>
            <MenuItem value="PUT">PUT</MenuItem>
            <MenuItem value="DELETE">DELETE</MenuItem>
            <MenuItem value="PATCH">PATCH</MenuItem>
            <MenuItem value="HEAD">HEAD</MenuItem>
            <MenuItem value="OPTIONS">OPTIONS</MenuItem>
          </Select>
        </FormControl>
        <TextField
          className={classes.urlField}
          label="URL"
          variant="outlined"
          value={currentRequest.url}
          onChange={handleUrlChange}
          placeholder="https://api.example.com/endpoint"
          helperText={getUrlHelperText(currentRequest.url)}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={isLoading ? <CircularProgress size={20} /> : <SendIcon />}
          onClick={onSendRequest}
          disabled={isLoading || !currentRequest.url}
          className={classes.sendButton}
        >
          {isLoading ? 'Sending...' : 'Send'}
        </Button>
      </div>

      {/* Request Configuration Tabs */}
      <Box>
        <Tabs
          value={tabValue}
          onChange={onTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Params" />
          <Tab label="Headers" />
          <Tab label="Body" />
          <Tab label="Auth" />
          <Tab label="Pre-request Script" />
          <Tab label="Tests" />
        </Tabs>

        {/* Params tab */}
        <TabPanel value={tabValue} index={0}>
          <ParamsTab
            params={currentRequest.params}
            onChange={handleParamsChange}
            environment={selectedEnvironment}
          />
        </TabPanel>

        {/* Headers tab */}
        <TabPanel value={tabValue} index={1}>
          <HeadersTab
            headers={currentRequest.headers}
            onChange={handleHeadersChange}
            environment={selectedEnvironment}
          />
        </TabPanel>

        {/* Body tab */}
        <TabPanel value={tabValue} index={2}>
          <BodyTab
            body={currentRequest.body}
            onChange={handleBodyChange}
            environment={selectedEnvironment}
          />
        </TabPanel>

        {/* Auth tab */}
        <TabPanel value={tabValue} index={3}>
          <AuthTab
            auth={currentRequest.auth}
            onChange={handleAuthChange}
            environment={selectedEnvironment}
          />
        </TabPanel>

        {/* Pre-request Script tab */}
        <TabPanel value={tabValue} index={4}>
          <PreRequestScriptPanel
            script={currentRequest.preRequestScript || ''}
            onSave={onSavePreRequestScript}
            isLoading={false}
            error={null}
          />
        </TabPanel>

        {/* Tests tab */}
        <TabPanel value={tabValue} index={5}>
          <TestGeneratorPanel
            request={currentRequest}
            response={null}
            onRunTests={() => {}}
            onSaveTests={onSaveTests}
            isGenerating={false}
            isRunning={false}
            error={null}
          />
        </TabPanel>
      </Box>
    </Paper>
  );
};
