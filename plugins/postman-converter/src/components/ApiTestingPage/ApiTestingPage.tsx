import React, { useState, useCallback } from 'react';
import {
  Grid,
  Typography,
  makeStyles,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';

// Extracted Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentManager } from './components/EnvironmentManager';
import { ContextMenu } from './components/ContextMenu';

// Existing Dialog Components
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';

// Custom Hooks
import { useCollections } from './hooks/useCollections';
import { useEnvironments } from './hooks/useEnvironments';
import { useRequest } from './hooks/useRequest';

// Types
import {
  SnackbarState,
  RenameDialogData,
  ContextMenuState,
  ApiRequest,
} from './types';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  // State for dialogs
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [editCollectionId, setEditCollectionId] = useState<string>('');
  const [editCollectionName, setEditCollectionName] = useState<string>('');
  const [editCollectionDescription, setEditCollectionDescription] = useState<string>('');
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState<boolean>(false);
  const [renameDialogData, setRenameDialogData] = useState<RenameDialogData | null>(null);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState<boolean>(false);
  const [createFolderParentId, setCreateFolderParentId] = useState<string | null>(null);
  const [createFolderCollectionId, setCreateFolderCollectionId] = useState<string>('');
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState<boolean>(false);
  const [createRequestParentId, setCreateRequestParentId] = useState<string | null>(null);
  const [createRequestCollectionId, setCreateRequestCollectionId] = useState<string>('');

  // State for context menu
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);

  // State for snackbar
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Snackbar callback
  const handleSnackbar = useCallback((message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  }, []);

  // Request selection callback
  const handleRequestSelect = useCallback((request: ApiRequest) => {
    // This will be handled by the request hook when it's properly implemented
  }, []);

  // Custom hooks for state management
  const collectionsHook = useCollections(handleRequestSelect, handleSnackbar);
  const environmentsHook = useEnvironments();
  const requestHook = useRequest();

  // Context menu handlers
  const handleContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Dialog handlers
  const handleAddFolder = useCallback((collectionId: string, parentFolderId?: string) => {
    setCreateFolderCollectionId(collectionId);
    setCreateFolderParentId(parentFolderId || null);
    setCreateFolderDialogOpen(true);
    handleContextMenuClose();
  }, [handleContextMenuClose]);

  const handleAddRequest = useCallback((collectionId: string, folderId?: string) => {
    setCreateRequestCollectionId(collectionId);
    setCreateRequestParentId(folderId || null);
    setCreateRequestDialogOpen(true);
    handleContextMenuClose();
  }, [handleContextMenuClose]);

  const handleRenameCollection = useCallback((collectionId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'collection',
      itemId: collectionId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
    handleContextMenuClose();
  }, [handleContextMenuClose]);

  const handleRenameFolder = useCallback((collectionId: string, folderId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'folder',
      itemId: folderId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
    handleContextMenuClose();
  }, [handleContextMenuClose]);

  const handleRenameRequest = useCallback((collectionId: string, requestId: string, currentName: string) => {
    setRenameDialogData({
      itemType: 'request',
      itemId: requestId,
      collectionId,
      currentName,
    });
    setRenameDialogOpen(true);
    handleContextMenuClose();
  }, [handleContextMenuClose]);

  const handleEditCollection = useCallback((collectionId: string) => {
    const collection = collectionsHook.collections.find(col => col.id === collectionId);
    if (collection) {
      setEditCollectionId(collectionId);
      setEditCollectionName(collection.name);
      setEditCollectionDescription(collection.description);
      setEditDialogOpen(true);
    }
    handleContextMenuClose();
  }, [collectionsHook.collections, handleContextMenuClose]);

  // Snackbar close handler
  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  return (
    <div className={classes.root}>
      <InfoCard title="API Testing" className={classes.infoCard}>
        <Typography variant="body1">
          Test your APIs with a Postman-like interface. Create collections, organize requests, and run tests.
        </Typography>
      </InfoCard>

      {/* Environment Manager */}
      <EnvironmentManager
        environments={environmentsHook.environments}
        currentEnvironment={environmentsHook.currentEnvironment}
        selectedEnvironmentId={environmentsHook.selectedEnvironmentId}
        environmentDialogOpen={environmentsHook.environmentDialogOpen}
        onEnvironmentChange={environmentsHook.handleEnvironmentChange}
        onEnvironmentsUpdate={environmentsHook.setEnvironments}
        onSelectedEnvironmentChange={environmentsHook.setSelectedEnvironmentId}
        onDialogToggle={environmentsHook.setEnvironmentDialogOpen}
      />

      <Grid container spacing={3}>
        {/* Collections Sidebar */}
        <Grid item xs={12} md={4}>
          <CollectionsSidebar
            collections={collectionsHook.collections}
            selectedItemId={collectionsHook.selectedItemId}
            expandedFolders={collectionsHook.expandedFolders}
            unsavedCollections={collectionsHook.unsavedCollections}
            isSaving={collectionsHook.isSaving}
            collectionsLoading={collectionsHook.collectionsLoading}
            collectionsError={collectionsHook.collectionsError}
            onItemSelect={collectionsHook.handleItemSelect}
            onFolderToggle={collectionsHook.handleFolderToggle}
            onContextMenu={handleContextMenu}
            onSaveCollection={collectionsHook.handleSaveCollection}
            onAddCollection={collectionsHook.handleAddCollection}
          />
        </Grid>

        {/* Request and Response Panels */}
        <Grid item xs={12} md={8}>
          {/* Request Panel */}
          <RequestPanel
            currentRequest={requestHook.currentRequest}
            selectedEnvironment={environmentsHook.selectedEnvironment}
            tabValue={requestHook.tabValue}
            isLoading={requestHook.isLoading}
            onRequestChange={requestHook.setCurrentRequest}
            onTabChange={requestHook.handleTabChange}
            onSendRequest={requestHook.handleSendRequest}
            onSaveTests={() => {}}
            onSavePreRequestScript={() => {}}
          />

          {/* Response Panel */}
          <ResponsePanel
            currentResponse={requestHook.currentResponse}
            responseTabValue={requestHook.responseTabValue}
            testResults={[]}
            isRunningTests={false}
            testError={null}
            onResponseTabChange={(event, newValue) => requestHook.setResponseTabValue(newValue)}
            onRunTests={() => {}}
          />
        </Grid>
      </Grid>

      {/* Context Menu */}
      <ContextMenu
        contextMenu={contextMenu}
        collections={collectionsHook.collections}
        onClose={handleContextMenuClose}
        onAddFolder={handleAddFolder}
        onAddRequest={handleAddRequest}
        onRenameCollection={handleRenameCollection}
        onRenameFolder={handleRenameFolder}
        onRenameRequest={handleRenameRequest}
        onEditCollection={handleEditCollection}
        onDuplicateRequest={collectionsHook.handleDuplicateRequest}
        onDeleteItem={collectionsHook.handleDeleteItem}
      />

      {/* Dialogs */}
      <CreateFolderDialog
        open={createFolderDialogOpen}
        onClose={() => setCreateFolderDialogOpen(false)}
        onCreateFolder={(folderName) => {
          collectionsHook.handleCreateFolder(folderName, createFolderParentId, createFolderCollectionId);
          setCreateFolderDialogOpen(false);
        }}
      />

      <CreateRequestDialog
        open={createRequestDialogOpen}
        onClose={() => setCreateRequestDialogOpen(false)}
        onCreateRequest={(requestName, method, url) => {
          collectionsHook.handleCreateRequest(requestName, method, url, createRequestParentId, createRequestCollectionId);
          setCreateRequestDialogOpen(false);
        }}
      />

      <RenameDialog
        open={renameDialogOpen}
        onClose={() => setRenameDialogOpen(false)}
        onRename={(newName) => {
          if (renameDialogData) {
            // Handle rename logic here
            setRenameDialogOpen(false);
          }
        }}
        currentName={renameDialogData?.currentName || ''}
        itemType={renameDialogData?.itemType || 'collection'}
      />

      <ImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImportCollection={(collection) => {
          // Handle import logic here
          setImportDialogOpen(false);
        }}
        onImportEnvironment={(environment) => {
          environmentsHook.setEnvironments([...environmentsHook.environments, environment]);
          setImportDialogOpen(false);
        }}
      />

      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        collections={collectionsHook.collections}
        environments={environmentsHook.environments}
      />

      {/* Edit Collection Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)}>
        <DialogTitle>Edit Collection</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Collection Name"
            fullWidth
            value={editCollectionName}
            onChange={(e) => setEditCollectionName(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={editCollectionDescription}
            onChange={(e) => setEditCollectionDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={() => {
            // Handle save logic here
            setEditDialogOpen(false);
          }} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};
